
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Description, Dialog, DialogPanel, DialogTitle } from '@headlessui/react'
import { CheckIcon } from "@heroicons/react/24/solid";
import { ExclamationCircleIcon } from "@heroicons/react/24/solid";
import Select from "react-select";


import Link from 'next/link';
// import { wordpressDefaults } from '@/app/lib/config';
// import { Button } from '@/app/ui/button';


interface Template {
    id: string;
    name: string;
}



export default function NewTemplatePage() {
    const router = useRouter();
    const [error, setError] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState<boolean>(false)
    const [formData, setFormData] = useState({
        name: '',
        subName: '',
        description: '',
        deploymentTemplate: '',
    });
    const [templates, setTemplates] = useState<Template[]>([]);

    useEffect(() => {
        async function fetchTemplates() {
            try {
                const response = await fetch('/api/templates');
                if (!response.ok) {
                    throw new Error('Failed to fetch templates');
                }
                const data = await response.json();
                setTemplates(data);
            } catch (err) {
                setError('Error loading templates');
                console.error(err);
            }
        }
        fetchTemplates();
    }, []);


    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const checkDomainResolution = async () => {
        setIsOpen(true);
        try {
            const response = await fetch('/api/check-domain', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ domain: formData.name }),
            });

            if (!response.ok) {
                // throw new Error('Failed to check domain');
                setIsOpen(true);
                // return
            }

            const data = await response.json();
            if (data.resolvable) {
                // alert('Domain is resolvable');
                setIsOpen(true);
            } else {
                alert('Domain is not resolvable');
            }
        } catch (err) {
            console.error('Error checking domain:', err);
        }
    };

    const close = () => {
        setIsOpen(false)
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            const response = await fetch('/api/sites', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '创建wp站点失败');
            }

            const data = await response.json();

            if (data.success) {
                router.push('/dashboard/sites');
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        } catch (err) {
            console.error('Error submitting form:', err);
            setError(err instanceof Error ? err.message : '创建wp站点失败');
        }
    };
    const handleDeactivate = async () => {
        await fetch('/deactivate-account', { method: 'POST' })
        setIsOpen(false)
    }


    return (
        <>
            <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
                <div className="fixed inset-0 flex  w-screen bg-black/50  items-center justify-center p-4" aria-hidden="true">
                    <DialogPanel className="max-w-4xl space-y-4 border bg-neutral-50 p-12">
                        <DialogTitle className="font-bold flex items-center gap-2">
                            <ExclamationCircleIcon className="h-6 w-6 text-red-500" aria-hidden="true" />
                            当前子域名已经被解析到其他IP，是否强行删除解析并继续创建站点？
                        </DialogTitle>
                        <div className="flex gap-4 justify-center">
                            <button className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" onClick={() => setIsOpen(false)}>取消</button>
                            <button className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" onClick={() => setIsOpen(false)}>确定</button>
                        </div>
                    </DialogPanel>
                </div>
            </Dialog>


            <div className="w-full">
                <div className="mb-6">
                    <h1 className="text-2xl font-bold">创建站点</h1>
                    <p className="text-gray-600">创建新的站点</p>
                </div>

                {error && (
                    <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                        <p>{error}</p>
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">

                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700">站点域名</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label htmlFor="subName" className="block text-sm font-medium text-gray-700">站点子域名</label>
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    id="subName"
                                    name="subName"
                                    value={formData.subName}
                                    onChange={handleChange}
                                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                                    required
                                />
                                <button
                                    type="button"
                                    onClick={checkDomainResolution}
                                    className="mt-1 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 whitespace-nowrap"
                                >
                                    检查域名解析
                                </button>
                            </div>
                        </div>
                        <div>
                            <label htmlFor="cloudPlatform" className="block text-sm font-medium text-gray-700">部署模板</label>
                            <select
                                id="deploymentTemplate"
                                name="deploymentTemplate"
                                value={formData.deploymentTemplate}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                            >
                                {templates.map((template) => (
                                    <option key={template.id} value={template.id}>
                                        {template.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div className="md:col-span-2">
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">描述</label>
                            <textarea
                                id="description"
                                name="description"
                                value={formData.description}
                                onChange={handleChange}
                                rows={3}
                                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                            />
                        </div>



                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/dashboard/sites"
                                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                取消
                            </Link>
                            <button
                                type="submit"
                                className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                创建站点
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </>
    );
}
