generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
}

model User {
  id       String @id @default(uuid())
  name     String
  email    String @unique
  password String
}

model CloudProvider {
  id           String  @id @default(uuid())
  name         String
  type         String
  accountId    String? @map("account_id")
  apiKey       String? @map("api_key")
  apiSecret    String? @map("api_secret")
  region       String?
  active       Boolean @default(true)
  capabilities Json    @default("{}")
  settings     Json?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  servers        Server[]
  rdsInstances   RdsInstance[]
  loadBalancers  LoadBalancer[]
  cloudfronts    Cloudfront[]

  @@map("cloud_providers")
}

model Server {
  id         String  @id @default(uuid())
  name       String
  providerId String? @map("provider_id")
  instanceId String? @map("instance_id")
  ipAddress  String? @map("ip_address")
  type       String
  status     String  @default("active")
  specs      Json?
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  provider      CloudProvider? @relation(fields: [providerId], references: [id])
  wordpressSites WordpressSite[]

  @@map("servers")
}

model BusinessGroup {
  id              String @id @default(uuid())
  name            String @unique
  code            String @unique // e.g., 'edm', 'shop', 'marketing'
  description     String?
  cloudPlatform   String @map("cloud_platform")
  deploymentType  String @map("deployment_type")
  environmentType String @map("environment_type")
  dnsProvider     String @map("dns_provider")
  apiEndpoint     String @map("api_endpoint")
  active          Boolean @default(true)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  templates      DeploymentTemplate[]
  wordpressSites WordpressSite[]

  @@map("business_groups")
}

model DeploymentTemplate {
  id              String @id @default(uuid())
  name            String
  businessGroupId String @map("business_group_id")
  description     String?
  wpVersion       String @map("wp_version")
  settings        Json?
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  businessGroup  BusinessGroup @relation(fields: [businessGroupId], references: [id])
  wordpressSites WordpressSite[]

  @@map("deployment_templates")
  DeploymentStep DeploymentStep[]
}

model WordpressSite {
  id               String  @id @default(uuid())
  name             String
  subName          String? @map("sub_name")
  domain           String?
  serverId         String? @map("server_id")
  rdsAccountId     String? @map("rds_account_id")
  loadBalancerId   String? @map("load_balancer_id")
  templateId       String? @map("template_id")
  adminUrl         String? @map("admin_url")
  adminUser        String? @map("admin_user")
  adminPassword    String? @map("admin_password")
  description      String?
  version          String?
  phpVersion       String? @map("php_version")
  status           String  @default("active")
  cdnEnabled       Boolean @default(false) @map("cdn_enabled")
  cdnProvider      String? @map("cdn_provider")
  cdnConfig        Json? @map("cdn_config")
  deploymentStatus String  @default("pending") @map("deployment_status")
  deploymentLog    String? @map("deployment_log")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  server       Server?            @relation(fields: [serverId], references: [id])
  rdsAccount   RdsAccount?        @relation(fields: [rdsAccountId], references: [id])
  loadBalancer LoadBalancer?      @relation(fields: [loadBalancerId], references: [id])
  template     DeploymentTemplate? @relation(fields: [templateId], references: [id])

  @@map("wordpress_sites")
  BusinessGroup BusinessGroup[]
}

model RdsInstance {
  id         String  @id @default(uuid())
  providerId String? @map("provider_id")
  instanceId String  @map("instance_id")
  name       String
  endpoint   String?
  port       Int     @default(3306)
  engine     String  @default("MySQL")
  engineVersion String? @map("engine_version")
  status     String  @default("active")
  createdAt  DateTime @default(now()) @map("created_at")

  provider   CloudProvider? @relation(fields: [providerId], references: [id])
  accounts   RdsAccount[]

  @@map("rds_instances")
}

model RdsAccount {
  id            String  @id @default(uuid())
  rdsInstanceId String  @map("rds_instance_id")
  username      String
  password      String?
  privileges    String  @default("ReadWrite")
  status        String  @default("active")
  createdAt     DateTime @default(now()) @map("created_at")

  rdsInstance   RdsInstance @relation(fields: [rdsInstanceId], references: [id], onDelete: Cascade)
  wordpressSites WordpressSite[]

  @@map("rds_accounts")
}

model LoadBalancer {
  id         String  @id @default(uuid())
  providerId String? @map("provider_id")
  lbId       String  @map("lb_id")
  name       String
  type       String  @default("Application")
  ipAddress  String? @map("ip_address")
  dnsName    String? @map("dns_name")
  status     String  @default("active")
  settings   Json?
  createdAt  DateTime @default(now()) @map("created_at")

  provider   CloudProvider? @relation(fields: [providerId], references: [id])
  listeners  LbListener[]
  wordpressSites WordpressSite[]

  @@map("load_balancers")
}

model LbListener {
  id             String  @id @default(uuid())
  loadBalancerId String  @map("load_balancer_id")
  protocol       String
  port           Int
  targetGroup    String? @map("target_group")
  certificateArn String? @map("certificate_arn")
  createdAt      DateTime @default(now()) @map("created_at")

  loadBalancer   LoadBalancer @relation(fields: [loadBalancerId], references: [id], onDelete: Cascade)

  @@map("lb_listeners")
}

model Cloudfront {
  id                String  @id @default(uuid())
  providerId        String? @map("provider_id")
  cloudfrontId      String  @map("cloudfront_id")
  originDomain      String? @map("origin_domain")
  distributionStatus String  @map("distribution_status")
  settings          Json
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  provider          CloudProvider? @relation(fields: [providerId], references: [id])

  @@map("cloudfronts")
}

model DeploymentStep {
  id         String @id @default(uuid())
  templateId String @map("template_id")
  stepOrder  Int    @map("step_order")
  name       String
  action     String
  parameters Json?

  template   DeploymentTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@map("deployment_steps")
}

