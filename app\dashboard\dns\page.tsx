
'use client';

import { useState, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  PlusIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { dnsConfig } from '@/app/lib/config';

interface DnsRecord {
  id: string;
  domain: string;
  type: string;
  value: string;
  ttl: number;
  status: 'active' | 'pending' | 'error';
  provider: string;
  createdAt: string;
}

interface DomainResolution {
  dnsType: any;
  domain: string;
  resolved: boolean;
  records: string[];
}

interface SslInfo {
  domain: string;
  status: 'active' | 'pending' | 'error' | 'none';
  issuer: string | null;
  expiresAt: string | null;
  type: 'flexible' | 'full' | 'strict' | 'none';
}

export default function DnsManagerPage() {
  const [records, setRecords] = useState<DnsRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [checkDomain, setCheckDomain] = useState('');
  const [resolutionResult, setResolutionResult] = useState<DomainResolution | null>(null);
  const [sslResult, setSslResult] = useState<SslInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newRecord, setNewRecord] = useState({
    domain: '',
    type: 'A',
    value: '',
    ttl: 300,
    provider: 'cloudflare'
  });

  useEffect(() => {
    fetchDnsRecords();
  }, []);

  const fetchDnsRecords = async () => {
    try {
      const response = await fetch(dnsConfig.records);
      if (!response.ok) throw new Error('Failed to fetch DNS records');
      const data = await response.json();
      setRecords(data.records || []);
    } catch (error) {
      console.error('Error fetching DNS records:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkDomainResolution = async () => {
    if (!checkDomain.trim()) return;
    
    setIsChecking(true);
    try {
      const response = await fetch(dnsConfig.checkResolution, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: checkDomain })
      });
      
      if (!response.ok) throw new Error('Failed to check domain resolution');
      const data = await response.json();
      setResolutionResult(data);
    } catch (error) {
      console.error('Error checking domain:', error);
      setResolutionResult(null);
    } finally {
      setIsChecking(false);
    }
  };

  const checkSslStatus = async () => {
    if (!checkDomain.trim()) return;
    
    setIsChecking(true);
    try {
      const response = await fetch(dnsConfig.checkSsl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: checkDomain })
      });
      
      if (!response.ok) throw new Error('Failed to check SSL status');
      const data = await response.json();
      setSslResult(data);
    } catch (error) {
      console.error('Error checking SSL:', error);
      setSslResult(null);
    } finally {
      setIsChecking(false);
    }
  };

  const addDnsRecord = async () => {
    try {
      const response = await fetch(dnsConfig.updateRecord, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newRecord)
      });
      
      if (!response.ok) throw new Error('Failed to add DNS record');
      
      setShowAddForm(false);
      setNewRecord({
        domain: '',
        type: 'A',
        value: '',
        ttl: 300,
        provider: 'cloudflare'
      });
      fetchDnsRecords();
    } catch (error) {
      console.error('Error adding DNS record:', error);
    }
  };

  const deleteDnsRecord = async (id: string) => {
    if (!confirm('确定要删除这条DNS记录吗？')) return;
    
    try {
      const response = await fetch(`${dnsConfig.deleteRecord}/${id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) throw new Error('Failed to delete DNS record');
      fetchDnsRecords();
    } catch (error) {
      console.error('Error deleting DNS record:', error);
    }
  };

  return (
    <div className="w-full space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">域名解析管理</h1>
          <p className="text-gray-600">管理DNS记录、检查域名解析和SSL状态</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 flex items-center gap-2"
        >
          <PlusIcon className="h-5 w-5" />
          添加DNS记录
        </button>
      </div>

      {/* Domain Check Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-4">域名检查</h2>
        <div className="flex gap-4 mb-4">
          <input
            type="text"
            value={checkDomain}
            onChange={(e) => setCheckDomain(e.target.value)}
            placeholder="输入域名 (例如: example.com)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={checkDomainResolution}
            disabled={isChecking}
            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 disabled:opacity-50 flex items-center gap-2"
          >
            <MagnifyingGlassIcon className="h-5 w-5" />
            检查解析
          </button>
          <button
            onClick={checkSslStatus}
            disabled={isChecking}
            className="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 disabled:opacity-50 flex items-center gap-2"
          >
            <ShieldCheckIcon className="h-5 w-5" />
            检查SSL
          </button>
        </div>

        {/* Resolution Results */}
        {resolutionResult && (
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2 flex items-center gap-2">
              {resolutionResult.resolved ? (
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
              )}
              解析结果: {resolutionResult.domain}
            </h3>
            <div className="text-sm space-y-1">
              <p>状态: {resolutionResult.resolved ? '已解析' : '未解析'}</p>
              {resolutionResult.dnsType && <p>DNS类型: {resolutionResult.dnsType}</p>}
              {resolutionResult.records.length > 0 && (
                <div>
                  <p className="font-medium">DNS记录:</p>
                  {resolutionResult.records.map((record, index) => (
                    <p key={index} className="ml-4">
                      {record}
                    </p>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* SSL Results */}
        {sslResult && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2 flex items-center gap-2">
              <ShieldCheckIcon className="h-5 w-5 text-blue-500" />
              SSL状态: {sslResult.domain}
            </h3>
            <div className="text-sm space-y-1">
              <p>状态: {sslResult.status}</p>
              <p>类型: {sslResult.type}</p>
              {sslResult.issuer && <p>颁发者: {sslResult.issuer}</p>}
              {sslResult.expiresAt && <p>过期时间: {new Date(sslResult.expiresAt).toLocaleDateString()}</p>}
            </div>
          </div>
        )}
      </div>

      {/* Add Record Form */}
      {showAddForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-4">添加DNS记录</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">域名</label>
              <input
                type="text"
                value={newRecord.domain}
                onChange={(e) => setNewRecord({...newRecord, domain: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="example.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">记录类型</label>
              <select
                value={newRecord.type}
                onChange={(e) => setNewRecord({...newRecord, type: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="A">A</option>
                <option value="AAAA">AAAA</option>
                <option value="CNAME">CNAME</option>
                <option value="MX">MX</option>
                <option value="TXT">TXT</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">记录值</label>
              <input
                type="text"
                value={newRecord.value}
                onChange={(e) => setNewRecord({...newRecord, value: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="192.168.1.1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">TTL</label>
              <input
                type="number"
                value={newRecord.ttl}
                onChange={(e) => setNewRecord({...newRecord, ttl: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">DNS提供商</label>
              <select
                value={newRecord.provider}
                onChange={(e) => setNewRecord({...newRecord, provider: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="cloudflare">Cloudflare</option>
                <option value="aliyun_dns">阿里云DNS</option>
                <option value="route53">AWS Route 53</option>
              </select>
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-4">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={addDnsRecord}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              添加记录
            </button>
          </div>
        </div>
      )}

      {/* DNS Records Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">DNS查询历史记录列表</h2>
        </div>
        
        {loading ? (
          <div className="text-center py-10">加载中...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录值</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TTL</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提供商</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {records.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {record.domain}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.value}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.ttl}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        record.status === 'active' ? 'bg-green-100 text-green-800' :
                        record.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {record.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.provider}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => deleteDnsRecord(record.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

